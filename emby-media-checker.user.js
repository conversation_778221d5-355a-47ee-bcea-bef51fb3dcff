// ==UserScript==
// @name         Emby 媒体库批量检查器
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  批量检查 Emby 媒体库中的媒体存在状态，支持目录树解析和精确匹配
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置管理
    const CONFIG_KEYS = {
        EMBY_HOST: 'emby_host',
        EMBY_API_KEY: 'emby_api_key',
        FAB_POSITION: 'fab_position'
    };

    // 状态管理
    let isChecking = false;
    let checkResults = [];
    let currentProgress = { current: 0, total: 0 };

    // UI 元素引用
    let fabButton, mainPanel, progressBar, resultsContainer;

    // 样式定义
    const styles = `
        .emby-checker-fab {
            position: fixed;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            border: none;
            cursor: pointer;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .emby-checker-fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
        }

        .emby-checker-fab.checking {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            animation: pulse 2s infinite;
        }

        .emby-checker-fab.completed {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .emby-checker-panel {
            position: fixed;
            width: 380px;
            max-height: 600px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 10001;
            display: none;
            flex-direction: column;
            overflow: hidden;
            transform: scale(0.8) translateY(20px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .emby-checker-panel.show {
            display: flex;
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        .panel-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .panel-content {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }

        .config-section {
            margin-bottom: 20px;
        }

        .config-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .config-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }

        .config-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .input-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: monospace;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .progress-section {
            margin: 16px 0;
            display: none;
        }

        .progress-section.show {
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
        }

        .results-section {
            margin-top: 16px;
        }

        .result-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 4px solid;
            font-size: 13px;
            line-height: 1.4;
        }

        .result-found {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .result-not-found {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .result-error {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }

        .result-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .result-details {
            font-size: 12px;
            opacity: 0.8;
        }

        .emby-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .emby-link:hover {
            text-decoration: underline;
        }
    `;

    // 注入样式
    function injectStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    // 创建 FAB 按钮
    function createFAB() {
        fabButton = document.createElement('button');
        fabButton.className = 'emby-checker-fab';
        fabButton.innerHTML = '🎬';
        fabButton.title = 'Emby 媒体库检查器';
        
        // 设置初始位置
        const savedPosition = GM_getValue(CONFIG_KEYS.FAB_POSITION, { top: 100, right: 20 });
        fabButton.style.top = savedPosition.top + 'px';
        fabButton.style.right = savedPosition.right + 'px';
        
        // 点击事件
        fabButton.addEventListener('click', togglePanel);
        
        // 拖拽功能
        makeDraggable(fabButton, true);
        
        document.body.appendChild(fabButton);
    }

    // 创建主面板
    function createMainPanel() {
        mainPanel = document.createElement('div');
        mainPanel.className = 'emby-checker-panel';
        
        mainPanel.innerHTML = `
            <div class="panel-header">
                <h3 class="panel-title">🎬 Emby 媒体库检查器</h3>
                <button class="close-btn">×</button>
            </div>
            <div class="panel-content">
                <div class="config-section">
                    <label class="config-label">Emby 服务器地址:</label>
                    <input type="text" class="config-input" id="emby-host" placeholder="http://localhost:8096">
                </div>
                <div class="config-section">
                    <label class="config-label">API Key:</label>
                    <input type="text" class="config-input" id="emby-api-key" placeholder="你的 Emby API Key">
                </div>
                <div class="config-section">
                    <label class="config-label">媒体列表或目录树:</label>
                    <textarea class="config-input input-textarea" id="media-input" placeholder="粘贴媒体列表或目录树...&#10;支持格式:&#10;- 电影名称 (年份) {tmdbid-12345}&#10;- 目录树结构"></textarea>
                </div>
                <div class="config-section">
                    <button class="btn btn-primary" id="start-check">开始检查</button>
                    <button class="btn btn-secondary" id="clear-results">清空结果</button>
                    <button class="btn btn-secondary" id="save-config">保存配置</button>
                </div>
                <div class="progress-section" id="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">准备中...</div>
                </div>
                <div class="results-section" id="results-section"></div>
            </div>
        `;
        
        // 设置面板位置
        const fabRect = fabButton.getBoundingClientRect();
        mainPanel.style.top = Math.max(10, fabRect.top - 50) + 'px';
        mainPanel.style.right = Math.max(10, window.innerWidth - fabRect.left + 10) + 'px';
        
        document.body.appendChild(mainPanel);
        
        // 绑定事件
        bindPanelEvents();
        
        // 加载保存的配置
        loadConfig();
        
        // 拖拽功能
        makeDraggable(mainPanel, false, mainPanel.querySelector('.panel-header'));
    }

    // 绑定面板事件
    function bindPanelEvents() {
        // 关闭按钮
        mainPanel.querySelector('.close-btn').addEventListener('click', hidePanel);

        // 开始检查按钮
        document.getElementById('start-check').addEventListener('click', startMediaCheck);

        // 清空结果按钮
        document.getElementById('clear-results').addEventListener('click', clearResults);

        // 保存配置按钮
        document.getElementById('save-config').addEventListener('click', saveConfig);
    }

    // 拖拽功能实现
    function makeDraggable(element, isFAB = false, handle = null) {
        const dragHandle = handle || element;
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        dragHandle.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;

            const rect = element.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;

            dragHandle.style.cursor = 'grabbing';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            let newLeft = startLeft + deltaX;
            let newTop = startTop + deltaY;

            // 边界检查
            const maxLeft = window.innerWidth - element.offsetWidth;
            const maxTop = window.innerHeight - element.offsetHeight;

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            if (isFAB) {
                element.style.right = (window.innerWidth - newLeft - element.offsetWidth) + 'px';
                element.style.top = newTop + 'px';
            } else {
                element.style.left = newLeft + 'px';
                element.style.top = newTop + 'px';
                element.style.right = 'auto';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                dragHandle.style.cursor = 'grab';

                // 保存 FAB 位置
                if (isFAB) {
                    const rect = element.getBoundingClientRect();
                    GM_setValue(CONFIG_KEYS.FAB_POSITION, {
                        top: rect.top,
                        right: window.innerWidth - rect.right
                    });
                }
            }
        });
    }

    // 显示/隐藏面板
    function togglePanel() {
        if (mainPanel.classList.contains('show')) {
            hidePanel();
        } else {
            showPanel();
        }
    }

    function showPanel() {
        mainPanel.classList.add('show');
    }

    function hidePanel() {
        mainPanel.classList.remove('show');
    }

    // 配置管理
    function loadConfig() {
        document.getElementById('emby-host').value = GM_getValue(CONFIG_KEYS.EMBY_HOST, '');
        document.getElementById('emby-api-key').value = GM_getValue(CONFIG_KEYS.EMBY_API_KEY, '');
    }

    function saveConfig() {
        const host = document.getElementById('emby-host').value.trim();
        const apiKey = document.getElementById('emby-api-key').value.trim();

        GM_setValue(CONFIG_KEYS.EMBY_HOST, host);
        GM_setValue(CONFIG_KEYS.EMBY_API_KEY, apiKey);

        showNotification('配置已保存', 'success');
    }

    // 解析媒体输入
    function parseMediaInput(input) {
        const lines = input.split('\n').map(line => line.trim()).filter(line => line);
        const mediaItems = [];

        for (const line of lines) {
            // 跳过目录树结构行（以 | 开头的行）
            if (line.startsWith('|') || line.startsWith('├') || line.startsWith('└')) {
                continue;
            }

            // 解析媒体信息
            const mediaInfo = parseMediaLine(line);
            if (mediaInfo) {
                mediaItems.push(mediaInfo);
            }
        }

        return mediaItems;
    }

    // 解析单行媒体信息
    function parseMediaLine(line) {
        // 匹配格式: 名称 (年份) {tmdbid-12345} 或 名称 (年份) {tmdb-12345}
        const tmdbMatch = line.match(/^(.+?)\s*\((\d{4})\)\s*\{tmdb(?:id)?-(\d+)\}/);
        if (tmdbMatch) {
            return {
                title: tmdbMatch[1].trim(),
                year: parseInt(tmdbMatch[2]),
                tmdbId: tmdbMatch[3],
                originalLine: line
            };
        }

        // 匹配格式: 名称 (年份)
        const yearMatch = line.match(/^(.+?)\s*\((\d{4})\)/);
        if (yearMatch) {
            return {
                title: yearMatch[1].trim(),
                year: parseInt(yearMatch[2]),
                tmdbId: null,
                originalLine: line
            };
        }

        // 简单的标题匹配
        if (line.length > 0 && !line.includes('/') && !line.includes('\\')) {
            return {
                title: line.trim(),
                year: null,
                tmdbId: null,
                originalLine: line
            };
        }

        return null;
    }

    // Emby API 相关功能
    async function searchEmbyMedia(mediaItem) {
        const host = GM_getValue(CONFIG_KEYS.EMBY_HOST, '');
        const apiKey = GM_getValue(CONFIG_KEYS.EMBY_API_KEY, '');

        if (!host || !apiKey) {
            throw new Error('请先配置 Emby 服务器地址和 API Key');
        }

        try {
            // 优先使用 TMDB ID 进行精确查找
            if (mediaItem.tmdbId) {
                const exactResult = await searchByProviderId(host, apiKey, 'Tmdb', mediaItem.tmdbId);
                if (exactResult && exactResult.TotalRecordCount > 0) {
                    return {
                        found: true,
                        items: exactResult.Items,
                        method: 'TMDB ID'
                    };
                }
            }

            // 使用标题和年份进行搜索
            const searchResult = await searchByTitle(host, apiKey, mediaItem.title, mediaItem.year);
            if (searchResult && searchResult.TotalRecordCount > 0) {
                return {
                    found: true,
                    items: searchResult.Items,
                    method: '标题搜索'
                };
            }

            return {
                found: false,
                items: [],
                method: '未找到'
            };

        } catch (error) {
            throw new Error(`API 请求失败: ${error.message}`);
        }
    }

    // 通过 Provider ID 搜索
    function searchByProviderId(host, apiKey, provider, id) {
        return new Promise((resolve, reject) => {
            const url = `${host}/emby/Items?api_key=${apiKey}&AnyProviderIdEquals=${provider}.${id}&Recursive=true`;

            GM_xmlhttpRequest({
                method: 'GET',
                url: url,
                headers: {
                    'Accept': 'application/json'
                },
                onload: function(response) {
                    if (response.status === 200) {
                        try {
                            const data = JSON.parse(response.responseText);
                            resolve(data);
                        } catch (e) {
                            reject(new Error('解析响应失败'));
                        }
                    } else {
                        reject(new Error(`HTTP ${response.status}: ${response.statusText}`));
                    }
                },
                onerror: function() {
                    reject(new Error('网络请求失败'));
                },
                ontimeout: function() {
                    reject(new Error('请求超时'));
                },
                timeout: 10000
            });
        });
    }

    // 通过标题搜索
    function searchByTitle(host, apiKey, title, year) {
        return new Promise((resolve, reject) => {
            let searchTerm = encodeURIComponent(title);
            let url = `${host}/emby/Items?api_key=${apiKey}&SearchTerm=${searchTerm}&Recursive=true&IncludeItemTypes=Movie,Series`;

            if (year) {
                url += `&Years=${year}`;
            }

            GM_xmlhttpRequest({
                method: 'GET',
                url: url,
                headers: {
                    'Accept': 'application/json'
                },
                onload: function(response) {
                    if (response.status === 200) {
                        try {
                            const data = JSON.parse(response.responseText);
                            resolve(data);
                        } catch (e) {
                            reject(new Error('解析响应失败'));
                        }
                    } else {
                        reject(new Error(`HTTP ${response.status}: ${response.statusText}`));
                    }
                },
                onerror: function() {
                    reject(new Error('网络请求失败'));
                },
                ontimeout: function() {
                    reject(new Error('请求超时'));
                },
                timeout: 10000
            });
        });
    }

    // 开始媒体检查
    async function startMediaCheck() {
        const input = document.getElementById('media-input').value.trim();
        if (!input) {
            showNotification('请输入媒体列表', 'error');
            return;
        }

        const host = GM_getValue(CONFIG_KEYS.EMBY_HOST, '');
        const apiKey = GM_getValue(CONFIG_KEYS.EMBY_API_KEY, '');

        if (!host || !apiKey) {
            showNotification('请先配置 Emby 服务器地址和 API Key', 'error');
            return;
        }

        // 解析媒体列表
        const mediaItems = parseMediaInput(input);
        if (mediaItems.length === 0) {
            showNotification('未找到有效的媒体项目', 'error');
            return;
        }

        // 开始检查
        isChecking = true;
        checkResults = [];
        currentProgress = { current: 0, total: mediaItems.length };

        updateFABState('checking');
        showProgress();
        clearResults();

        // 并发控制 - 每次最多处理 3 个请求
        const concurrency = 3;
        const chunks = [];
        for (let i = 0; i < mediaItems.length; i += concurrency) {
            chunks.push(mediaItems.slice(i, i + concurrency));
        }

        try {
            for (const chunk of chunks) {
                const promises = chunk.map(item => checkSingleMedia(item));
                await Promise.allSettled(promises);

                // 更新进度
                currentProgress.current = Math.min(currentProgress.current + chunk.length, currentProgress.total);
                updateProgress();

                // 添加小延迟避免过于频繁的请求
                if (currentProgress.current < currentProgress.total) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            updateFABState('completed');
            showNotification(`检查完成！共检查 ${mediaItems.length} 个项目`, 'success');

        } catch (error) {
            showNotification(`检查过程中出现错误: ${error.message}`, 'error');
        } finally {
            isChecking = false;
            hideProgress();
        }
    }

    // 检查单个媒体项目
    async function checkSingleMedia(mediaItem) {
        try {
            const result = await searchEmbyMedia(mediaItem);
            const checkResult = {
                mediaItem: mediaItem,
                found: result.found,
                items: result.items,
                method: result.method,
                error: null
            };

            checkResults.push(checkResult);
            displayResult(checkResult);

        } catch (error) {
            const checkResult = {
                mediaItem: mediaItem,
                found: false,
                items: [],
                method: null,
                error: error.message
            };

            checkResults.push(checkResult);
            displayResult(checkResult);
        }
    }

    // 显示单个检查结果
    function displayResult(result) {
        const resultsContainer = document.getElementById('results-section');
        const resultElement = document.createElement('div');

        let className, statusText, detailsHtml = '';

        if (result.error) {
            className = 'result-error';
            statusText = '❌ 检查失败';
            detailsHtml = `<div class="result-details">错误: ${result.error}</div>`;
        } else if (result.found) {
            className = 'result-found';
            statusText = '✅ 已找到';

            if (result.items.length > 0) {
                const item = result.items[0];
                const host = GM_getValue(CONFIG_KEYS.EMBY_HOST, '');
                const embyUrl = `${host}/web/index.html#!/details?id=${item.Id}`;

                detailsHtml = `
                    <div class="result-details">
                        匹配方式: ${result.method} |
                        <a href="${embyUrl}" target="_blank" class="emby-link">在 Emby 中查看</a>
                        ${item.Path ? `<br>路径: ${item.Path}` : ''}
                    </div>
                `;
            }
        } else {
            className = 'result-not-found';
            statusText = '❌ 未找到';
            detailsHtml = `<div class="result-details">在 Emby 媒体库中未找到此项目</div>`;
        }

        resultElement.className = `result-item ${className}`;
        resultElement.innerHTML = `
            <div class="result-title">${statusText} ${result.mediaItem.title}</div>
            <div class="result-details">原始行: ${result.mediaItem.originalLine}</div>
            ${detailsHtml}
        `;

        resultsContainer.appendChild(resultElement);

        // 滚动到最新结果
        resultElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    // 进度管理
    function showProgress() {
        document.getElementById('progress-section').classList.add('show');
        updateProgress();
    }

    function hideProgress() {
        document.getElementById('progress-section').classList.remove('show');
    }

    function updateProgress() {
        const percentage = currentProgress.total > 0 ?
            (currentProgress.current / currentProgress.total) * 100 : 0;

        document.getElementById('progress-fill').style.width = percentage + '%';
        document.getElementById('progress-text').textContent =
            `已处理 ${currentProgress.current} / ${currentProgress.total} 项`;
    }

    // FAB 状态管理
    function updateFABState(state) {
        fabButton.className = 'emby-checker-fab';

        switch (state) {
            case 'checking':
                fabButton.classList.add('checking');
                fabButton.innerHTML = '⏳';
                fabButton.title = '正在检查中...';
                break;
            case 'completed':
                fabButton.classList.add('completed');
                fabButton.innerHTML = '✅';
                fabButton.title = '检查完成';
                // 3秒后恢复默认状态
                setTimeout(() => updateFABState('default'), 3000);
                break;
            default:
                fabButton.innerHTML = '🎬';
                fabButton.title = 'Emby 媒体库检查器';
        }
    }

    // 清空结果
    function clearResults() {
        checkResults = [];
        document.getElementById('results-section').innerHTML = '';
    }

    // 通知系统
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            z-index: 10002;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        // 设置颜色
        switch (type) {
            case 'success':
                notification.style.background = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
                break;
            case 'error':
                notification.style.background = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
                break;
            default:
                notification.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 初始化
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 注入样式
        injectStyles();

        // 创建 UI 元素
        createFAB();
        createMainPanel();

        console.log('Emby 媒体库检查器已加载');
    }

    // 启动脚本
    init();

})();
